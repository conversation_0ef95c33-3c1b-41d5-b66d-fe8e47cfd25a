name: 🚀 Deploy Divided Finance Master to GitHub Pages

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Deployment Environment'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    
    steps:
      - name: 📋 Job Information
        run: |
          echo "::group::📋 Job Information"
          echo "🎯 Repository: ${{ github.repository }}"
          echo "🌿 Branch: ${{ github.ref_name }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "👤 Actor: ${{ github.actor }}"
          echo "🎬 Event: ${{ github.event_name }}"
          echo "🏃 Run ID: ${{ github.run_id }}"
          echo "🔢 Run Number: ${{ github.run_number }}"
          echo "::endgroup::"

      - name: 📦 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 📦 Checkout Complete
        run: |
          echo "::group::📦 Repository Information"
          echo "✅ Repository checked out successfully!"
          echo "📁 Working directory: $(pwd)"
          echo "📊 Repository size: $(du -sh . | cut -f1)"
          echo "📄 Files count: $(find . -type f | wc -l)"
          echo "::endgroup::"

      - name: 🟢 Setup Node.js Environment
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 🟢 Node.js Setup Complete
        run: |
          echo "::group::🟢 Node.js Information"
          echo "✅ Node.js setup complete!"
          echo "📦 Node version: $(node --version)"
          echo "📦 NPM version: $(npm --version)"
          echo "💾 Cache location: $(npm config get cache)"
          echo "::endgroup::"

      - name: 📥 Install Dependencies
        run: |
          echo "::group::📥 Installing Dependencies"
          echo "🔄 Starting dependency installation..."
          echo "📦 Package manager: npm"
          echo "📄 Reading package.json..."
          
          # Show package.json info
          if [ -f package.json ]; then
            echo "✅ package.json found"
            echo "📝 Project name: $(node -p "require('./package.json').name")"
            echo "🏷️ Version: $(node -p "require('./package.json').version")"
            echo "📊 Dependencies count: $(node -p "Object.keys(require('./package.json').dependencies || {}).length")"
            echo "🛠️ DevDependencies count: $(node -p "Object.keys(require('./package.json').devDependencies || {}).length")"
          fi
          
          echo "🚀 Running npm ci..."
          npm ci --verbose
          echo "✅ Dependencies installed successfully!"
          echo "::endgroup::"

      - name: 📊 Dependency Analysis
        run: |
          echo "::group::📊 Dependency Analysis"
          echo "🔍 Analyzing installed packages..."
          echo "📦 Total packages: $(npm list --depth=0 2>/dev/null | grep -c "├\|└" || echo "0")"
          echo "💾 node_modules size: $(du -sh node_modules 2>/dev/null | cut -f1 || echo "N/A")"
          echo "🔒 Audit results:"
          npm audit --audit-level=moderate || echo "⚠️ Some vulnerabilities found (non-blocking)"
          echo "::endgroup::"

      - name: 🏗️ Build Application
        run: |
          echo "::group::🏗️ Building Application"
          echo "🚀 Starting build process..."
          echo "⚙️ Build environment: production"
          echo "🎯 Target: GitHub Pages"
          
          # Set environment variables for build
          export CI=true
          export GENERATE_SOURCEMAP=false
          export PUBLIC_URL="/$(echo ${{ github.repository }} | cut -d'/' -f2)"
          
          echo "🌐 Public URL: $PUBLIC_URL"
          echo "📦 CI Mode: $CI"
          echo "🗺️ Source Maps: $GENERATE_SOURCEMAP"
          
          echo "🔨 Running build command..."
          npm run build
          
          echo "✅ Build completed successfully!"
          echo "::endgroup::"

      - name: 📊 Build Analysis
        run: |
          echo "::group::📊 Build Analysis"
          echo "🔍 Analyzing build output..."
          
          if [ -d "build" ]; then
            echo "✅ Build directory created"
            echo "📁 Build directory size: $(du -sh build | cut -f1)"
            echo "📄 Files in build: $(find build -type f | wc -l)"
            echo "🎨 CSS files: $(find build -name "*.css" | wc -l)"
            echo "📜 JS files: $(find build -name "*.js" | wc -l)"
            echo "🖼️ Asset files: $(find build -name "*.png" -o -name "*.jpg" -o -name "*.svg" -o -name "*.ico" | wc -l)"
            
            echo "📋 Build contents:"
            ls -la build/
            
            echo "📊 Largest files:"
            find build -type f -exec du -h {} + | sort -rh | head -10
          else
            echo "❌ Build directory not found!"
            exit 1
          fi
          echo "::endgroup::"

      - name: 🔧 Configure GitHub Pages
        run: |
          echo "::group::🔧 GitHub Pages Configuration"
          echo "⚙️ Configuring for GitHub Pages deployment..."
          
          # Add .nojekyll file to disable Jekyll processing
          touch build/.nojekyll
          echo "✅ Created .nojekyll file"
          
          # Create CNAME file if custom domain is needed (optional)
          # echo "yourdomain.com" > build/CNAME
          
          # Add 404.html for SPA routing
          cp build/index.html build/404.html
          echo "✅ Created 404.html for SPA routing"
          
          echo "🌐 GitHub Pages configuration complete!"
          echo "::endgroup::"

      - name: 📤 Upload Build Artifacts
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./build
          
      - name: 📤 Upload Complete
        run: |
          echo "::group::📤 Upload Information"
          echo "✅ Build artifacts uploaded successfully!"
          echo "📦 Artifact name: github-pages"
          echo "📁 Source path: ./build"
          echo "🎯 Ready for deployment!"
          echo "::endgroup::"

  deploy:
    name: 🚀 Deploy to GitHub Pages
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
      - name: 🚀 Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        
      - name: 🎉 Deployment Success
        run: |
          echo "::group::🎉 Deployment Complete"
          echo "✅ Divided Finance Master deployed successfully!"
          echo "🌐 Live URL: ${{ steps.deployment.outputs.page_url }}"
          echo "📅 Deployed at: $(date)"
          echo "🎯 Environment: github-pages"
          echo "🔗 Visit your app: ${{ steps.deployment.outputs.page_url }}"
          echo ""
          echo "🎊 Congratulations! Your invoice generator is now live!"
          echo "::endgroup::"

  notify:
    name: 📢 Post-Deployment Notifications
    runs-on: ubuntu-latest
    needs: [build, deploy]
    if: always()
    
    steps:
      - name: 📊 Deployment Summary
        run: |
          echo "::group::📊 Deployment Summary"
          echo "🏗️ Build Status: ${{ needs.build.result }}"
          echo "🚀 Deploy Status: ${{ needs.deploy.result }}"
          echo "📅 Completed at: $(date)"
          echo "🎯 Repository: ${{ github.repository }}"
          echo "🌿 Branch: ${{ github.ref_name }}"
          echo "📝 Commit: ${{ github.sha }}"
          
          if [ "${{ needs.build.result }}" == "success" ] && [ "${{ needs.deploy.result }}" == "success" ]; then
            echo "🎉 Overall Status: ✅ SUCCESS"
            echo "🌟 Your Divided Finance Master is live and ready to use!"
          else
            echo "❌ Overall Status: FAILED"
            echo "🔧 Please check the logs above for details"
          fi
          echo "::endgroup::"

      - name: 🎯 Next Steps
        if: needs.build.result == 'success' && needs.deploy.result == 'success'
        run: |
          echo "::group::🎯 Next Steps"
          echo "✨ Your application is now live! Here's what you can do next:"
          echo ""
          echo "🔗 Share your app with others"
          echo "📱 Test on different devices and browsers"
          echo "🔧 Monitor performance and user feedback"
          echo "📈 Set up analytics (optional)"
          echo "🔒 Configure custom domain (optional)"
          echo "🚀 Continue developing new features"
          echo ""
          echo "💡 Pro tip: Any push to main/master will automatically redeploy!"
          echo "::endgroup::"
