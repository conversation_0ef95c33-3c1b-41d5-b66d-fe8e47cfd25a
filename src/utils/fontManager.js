// Robust font management system with fallbacks
import { Font } from '@react-pdf/renderer';

// Font configuration with multiple fallback URLs
const FONT_CONFIGS = {
  'Inter': {
    weights: {
      300: [
        'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuOKfAZ9hiA.woff2',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300&display=swap',
        // Fallback to system fonts
        null
      ],
      400: [
        'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
        'https://fonts.googleapis.com/css2?family=Inter:wght@400&display=swap',
        null
      ],
      500: [
        'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hiA.woff2',
        'https://fonts.googleapis.com/css2?family=Inter:wght@500&display=swap',
        null
      ],
      600: [
        'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2',
        'https://fonts.googleapis.com/css2?family=Inter:wght@600&display=swap',
        null
      ],
      700: [
        'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.woff2',
        'https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap',
        null
      ]
    }
  },
  'Source Sans Pro': {
    weights: {
      300: [
        'https://fonts.gstatic.com/s/sourcesanspro/v21/6xK1dSBYKcSV-LCoeQqfX1RYOo3qPZ7nsDNvgQ.woff2',
        null
      ],
      400: [
        'https://fonts.gstatic.com/s/sourcesanspro/v21/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7lujVj9w.woff2',
        null
      ],
      600: [
        'https://fonts.gstatic.com/s/sourcesanspro/v21/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwlxdu3cOWxw.woff2',
        null
      ],
      700: [
        'https://fonts.gstatic.com/s/sourcesanspro/v21/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwlxdu3cOWxw.woff2',
        null
      ]
    }
  },
  'Playfair Display': {
    weights: {
      400: [
        'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qO0isEw.woff2',
        null
      ],
      600: [
        'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0isEw.woff2',
        null
      ],
      700: [
        'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qEkisEw.woff2',
        null
      ]
    }
  }
};

// System font fallbacks
const SYSTEM_FONT_FALLBACKS = {
  'Inter': 'Helvetica',
  'Source Sans Pro': 'Arial',
  'Playfair Display': 'Times-Roman',
  'Courier New': 'Courier'
};

// Track registered fonts to avoid duplicates
const registeredFonts = new Set();

/**
 * Test if a font URL is accessible
 */
const testFontUrl = async (url) => {
  if (!url) return false;
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn(`Font URL test failed: ${url}`, error);
    return false;
  }
};

/**
 * Register a single font with fallback mechanism
 */
const registerFontWithFallback = async (fontFamily, weight, urls) => {
  const fontKey = `${fontFamily}-${weight}`;
  
  if (registeredFonts.has(fontKey)) {
    return true; // Already registered
  }

  // Try each URL in order
  for (const url of urls) {
    if (!url) {
      // Use system font fallback
      const systemFont = SYSTEM_FONT_FALLBACKS[fontFamily] || 'Helvetica';
      try {
        Font.register({
          family: fontFamily,
          src: systemFont,
          fontWeight: weight
        });
        registeredFonts.add(fontKey);
        console.log(`✅ Registered ${fontFamily} (${weight}) with system fallback: ${systemFont}`);
        return true;
      } catch (error) {
        console.warn(`Failed to register system font ${systemFont}:`, error);
        continue;
      }
    }

    try {
      // Test if URL is accessible
      const isAccessible = await testFontUrl(url);
      if (!isAccessible) {
        console.warn(`Font URL not accessible: ${url}`);
        continue;
      }

      Font.register({
        family: fontFamily,
        src: url,
        fontWeight: weight
      });
      
      registeredFonts.add(fontKey);
      console.log(`✅ Registered ${fontFamily} (${weight}) from: ${url}`);
      return true;
    } catch (error) {
      console.warn(`Failed to register font from ${url}:`, error);
      continue;
    }
  }

  console.error(`❌ Failed to register ${fontFamily} (${weight}) with any source`);
  return false;
};

/**
 * Register all fonts with robust fallback system
 */
export const registerAllFonts = async () => {
  console.log('🔤 Starting font registration with fallback system...');
  
  const registrationPromises = [];

  // Register each font family and weight
  for (const [fontFamily, config] of Object.entries(FONT_CONFIGS)) {
    for (const [weight, urls] of Object.entries(config.weights)) {
      registrationPromises.push(
        registerFontWithFallback(fontFamily, parseInt(weight), urls)
      );
    }
  }

  // Register common system fonts as fallbacks
  const systemFonts = [
    { family: 'Helvetica', src: 'Helvetica' },
    { family: 'Arial', src: 'Arial' },
    { family: 'Times-Roman', src: 'Times-Roman' },
    { family: 'Courier', src: 'Courier' },
    { family: 'Courier New', src: 'Courier' }, // Map Courier New to Courier
  ];

  for (const font of systemFonts) {
    if (!registeredFonts.has(font.family)) {
      try {
        Font.register({
          family: font.family,
          src: font.src
        });
        registeredFonts.add(font.family);
        console.log(`✅ Registered system font: ${font.family}`);
      } catch (error) {
        console.warn(`Failed to register system font ${font.family}:`, error);
      }
    }
  }

  // Wait for all registrations to complete
  const results = await Promise.allSettled(registrationPromises);
  
  const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
  const total = results.length;
  
  console.log(`🔤 Font registration complete: ${successful}/${total} fonts registered successfully`);
  
  return { successful, total, registeredFonts: Array.from(registeredFonts) };
};

/**
 * Get safe font family name (with fallback)
 */
export const getSafeFontFamily = (requestedFont) => {
  // Check if the requested font is registered
  const hasFont = Array.from(registeredFonts).some(font => 
    font.startsWith(requestedFont.replace(' ', ''))
  );
  
  if (hasFont) {
    return requestedFont;
  }
  
  // Return system fallback
  return SYSTEM_FONT_FALLBACKS[requestedFont] || 'Helvetica';
};

/**
 * Validate font availability
 */
export const validateFont = (fontFamily, weight = 400) => {
  const fontKey = `${fontFamily}-${weight}`;
  return registeredFonts.has(fontKey) || registeredFonts.has(fontFamily);
};

/**
 * Get font registration status
 */
export const getFontStatus = () => {
  return {
    registeredFonts: Array.from(registeredFonts),
    totalRegistered: registeredFonts.size,
    availableFamilies: [...new Set(Array.from(registeredFonts).map(font => font.split('-')[0]))]
  };
};

/**
 * Emergency font reset (if something goes wrong)
 */
export const resetFonts = () => {
  registeredFonts.clear();
  
  // Register only basic system fonts
  const basicFonts = [
    { family: 'Helvetica', src: 'Helvetica' },
    { family: 'Arial', src: 'Arial' },
    { family: 'Courier', src: 'Courier' }
  ];
  
  basicFonts.forEach(font => {
    try {
      Font.register(font);
      registeredFonts.add(font.family);
    } catch (error) {
      console.error(`Failed to register basic font ${font.family}:`, error);
    }
  });
  
  console.log('🔄 Fonts reset to basic system fonts');
};
