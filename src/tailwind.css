@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark mode variables */
:root {
  --dark-bg-primary: #1a1a1a;
  --dark-bg-secondary: #2d2d2d;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #a0aec0;
}

/* Custom max width for invoice */
.max-w-invoice {
  max-width: 1200px;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade transitions */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-in;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

/* PDF viewer styles */
.react-pdf__Page {
  margin-top: 10px;
}

.react-pdf__Page__canvas {
  margin: 0 auto;
  width: 100% !important;
  height: auto !important;
}

/* Dark mode styles */
.dark {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

.dark .dark\:bg-dark-bg-primary {
  background-color: var(--dark-bg-primary);
}

.dark .dark\:bg-dark-bg-secondary {
  background-color: var(--dark-bg-secondary);
}

.dark .dark\:text-dark-text-primary {
  color: var(--dark-text-primary);
}

.dark .dark\:text-dark-text-secondary {
  color: var(--dark-text-secondary);
}

/* Tooltip styles */
.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}