@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the landing page */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(94, 56, 26, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(94, 56, 26, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode glass effect */
.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dark mode variables */
:root {
  --dark-bg-primary: #1a1a1a;
  --dark-bg-secondary: #2d2d2d;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #a0aec0;
}

/* Custom max width for invoice */
.max-w-invoice {
  max-width: 1200px;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade transitions */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-in;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

/* PDF viewer styles */
.react-pdf__Page {
  margin-top: 10px;
}

.react-pdf__Page__canvas {
  margin: 0 auto;
  width: 100% !important;
  height: auto !important;
}

/* Dark mode styles */
.dark {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

.dark .dark\:bg-dark-bg-primary {
  background-color: var(--dark-bg-primary);
}

.dark .dark\:bg-dark-bg-secondary {
  background-color: var(--dark-bg-secondary);
}

.dark .dark\:text-dark-text-primary {
  color: var(--dark-text-primary);
}

.dark .dark\:text-dark-text-secondary {
  color: var(--dark-text-secondary);
}

/* Tooltip styles */
.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}