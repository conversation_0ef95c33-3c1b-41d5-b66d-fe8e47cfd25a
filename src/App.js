import './utils/fonts';
import React, { useState, useEffect } from 'react';
import InvoiceForm from './components/InvoiceForm';
import Settings from './components/Settings';
import InvoiceHistory from './components/InvoiceHistory';
import Navigation from './components/Navigation';
import Loading from './components/Loading';
import { ToastProvider, useToast } from './components/Toast';
import { loadFromLocalStorage, saveToLocalStorage } from './utils/storage';
import { defaultCompanyInfo, defaultTheme, fetchCurrencies } from './utils/defaults';
import { loadInvoiceHistory, saveInvoiceToHistory } from './utils/invoiceStorage';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import LandingPage from './LandingPage';
import Dashboard from './components/Dashboard';
import Clients from './Clients';
import InvoiceHistoryPage from './pages/InvoiceHistoryPage';

// Main App Component with Toast Integration
function AppContent() {
  const [isLoading, setIsLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [invoiceData, setInvoiceData] = useState({
    companyInfo: defaultCompanyInfo,
    selectedClient: null,
    clientList: [],
    items: [],
    invoiceNumber: '',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    terms: '',
    notes: '',
    currency: 'USD',
    theme: defaultTheme
  });
  const toast = useToast();

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load saved data from localStorage
        const savedData = loadFromLocalStorage('invoiceData');
        if (savedData) {
          setInvoiceData(prevData => ({
            ...prevData,
            ...savedData
          }));
        }

        // Load available currencies
        const currencies = await fetchCurrencies();
        if (currencies) {
          setInvoiceData(prevData => ({
            ...prevData,
            availableCurrencies: currencies
          }));
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  const handleUpdateInvoice = (updates) => {
    setInvoiceData(prevData => {
      const newData = { ...prevData, ...updates };
      saveToLocalStorage('invoiceData', newData);
      return newData;
    });
  };

  const handleSaveInvoice = () => {
    try {
      // Save current invoice to history
      const savedInvoice = saveInvoiceToHistory({
        ...invoiceData,
        savedAt: new Date().toISOString()
      });

      if (savedInvoice) {
        // Reset form or prepare for new invoice
        setInvoiceData(prevData => ({
          ...prevData,
          items: [],
          invoiceNumber: '',
          issueDate: new Date().toISOString().split('T')[0],
          dueDate: '',
          notes: ''
        }));

        // Show success toast notification
        toast.success('Invoice saved successfully!');
      } else {
        toast.error('Failed to save invoice. Please try again.');
      }
    } catch (error) {
      console.error('Error saving invoice:', error);
      toast.error('Error saving invoice. Please try again.');
    }
  };

  if (isLoading) {
    return <Loading fullScreen text="Loading Invoice Generator..." />;
  }

  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <ErrorBoundary>
        <Routes>
          {/* Landing page without navigation */}
          <Route path="/" element={<LandingPage />} />

          {/* All other routes with navigation and layout */}
          <Route path="/*" element={
            <div className="min-h-screen bg-gradient-to-b from-primary-50 to-secondary-100 dark:bg-gradient-dark">
              <Navigation />

              <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <Routes>
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/invoices" element={
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
                      <div className="p-6">
                        <div className="space-y-6">
                          <InvoiceForm
                            invoiceData={invoiceData}
                            onUpdateInvoice={handleUpdateInvoice}
                          />
                          <div className="flex justify-end">
                            <button
                              onClick={handleSaveInvoice}
                              className="px-6 py-3 text-base font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
                            >
                              💾 Save Invoice
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  } />
                  <Route path="/clients" element={<Clients />} />
                  <Route path="/history" element={<InvoiceHistoryPage />} />
                  <Route path="/settings" element={
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden p-6">
                      <div className="mb-6">
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Settings</h1>
                        <p className="text-gray-600 dark:text-gray-300">Configure your invoice preferences and company information.</p>
                      </div>
                      <button
                        onClick={() => setShowSettings(true)}
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
                      >
                        ⚙️ Open Settings
                      </button>
                    </div>
                  } />
                </Routes>
              </main>

              <footer className="bg-white dark:bg-gray-800 shadow-inner mt-8">
                <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      © {new Date().getFullYear()} Divided Finance Master. Open source invoicing system.
                    </p>
                    <div className="flex space-x-6">
                      <a href="#" className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200">
                        Terms
                      </a>
                      <a href="#" className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200">
                        Privacy
                      </a>
                      <a href="#" className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200">
                        Contact
                      </a>
                    </div>
                  </div>
                </div>
              </footer>
            </div>
          } />
        </Routes>

        {/* Settings Modal */}
        <Settings
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
          invoiceData={invoiceData}
          onUpdateInvoice={handleUpdateInvoice}
        />
      </ErrorBoundary>
    </BrowserRouter>
  );
}

// Main App Component with Toast Provider
export default function App() {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  );
}