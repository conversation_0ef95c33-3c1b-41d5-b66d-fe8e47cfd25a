import React, { useState, useEffect } from 'react';
import {
  loadClients,
  addClient,
  updateClient,
  deleteClient,
  validateClientData
} from '../../utils/clientStorage';
import { useToast } from '../Toast';

const ClientSection = ({ selectedClient, clientList, onUpdateInvoice }) => {
  const [isAddingClient, setIsAddingClient] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [unifiedClients, setUnifiedClients] = useState([]);
  const [newClient, setNewClient] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    company: '',
    notes: ''
  });
  const toast = useToast();

  // Load unified clients on mount
  useEffect(() => {
    const clients = loadClients();
    setUnifiedClients(clients);

    // Sync with invoice data if needed
    if (clientList && clientList.length > 0) {
      // Merge any clients that might only exist in invoice data
      const mergedClients = [...clients];
      clientList.forEach(invoiceClient => {
        if (!clients.find(c => c.id === invoiceClient.id)) {
          mergedClients.push(invoiceClient);
        }
      });
      setUnifiedClients(mergedClients);
    }
  }, [clientList]);

  const handleSelectClient = (client) => {
    onUpdateInvoice({ selectedClient: client });
  };

  const handleAddClient = async () => {
    const errors = validateClientData(newClient);
    if (errors.length > 0) {
      toast.error(errors[0]);
      return;
    }

    try {
      const addedClient = addClient(newClient);
      const updatedClients = loadClients();
      setUnifiedClients(updatedClients);

      // Update invoice data
      onUpdateInvoice({
        clientList: updatedClients,
        selectedClient: addedClient
      });

      setNewClient({ name: '', email: '', phone: '', address: '', company: '', notes: '' });
      setIsAddingClient(false);
      toast.success('Client added successfully!');
    } catch (error) {
      toast.error('Failed to add client');
    }
  };

  const handleEditClient = async () => {
    const errors = validateClientData(editingClient);
    if (errors.length > 0) {
      toast.error(errors[0]);
      return;
    }

    try {
      const updatedClient = updateClient(editingClient.id, editingClient);
      const updatedClients = loadClients();
      setUnifiedClients(updatedClients);

      // Update invoice data
      onUpdateInvoice({
        clientList: updatedClients,
        selectedClient: selectedClient?.id === editingClient.id ? updatedClient : selectedClient
      });

      setEditingClient(null);
      toast.success('Client updated successfully!');
    } catch (error) {
      toast.error('Failed to update client');
    }
  };

  const handleDeleteClient = async (clientId) => {
    try {
      deleteClient(clientId);
      const updatedClients = loadClients();
      setUnifiedClients(updatedClients);

      // Update invoice data
      onUpdateInvoice({
        clientList: updatedClients,
        selectedClient: selectedClient?.id === clientId ? null : selectedClient
      });

      toast.success('Client deleted successfully!');
    } catch (error) {
      toast.error('Failed to delete client');
    }
  };

  const filteredClients = unifiedClients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (client.company && client.company.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const inputClasses = "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-colors duration-200";
  const labelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300";

  return (
    <div>
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Client Information</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Select an existing client or add a new one.</p>
          </div>
          <button
            type="button"
            onClick={() => setIsAddingClient(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add New Client
          </button>
        </div>
      </div>

      <div className="p-6">
        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              className={`${inputClasses} pl-10`}
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Client List */}
        {!isAddingClient && (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {filteredClients.map((client) => (
              <div
                key={client.id}
                className={`relative rounded-lg border ${
                  selectedClient?.id === client.id
                    ? 'border-primary-500 ring-2 ring-primary-500'
                    : 'border-gray-300 dark:border-gray-600'
                } p-4 hover:border-primary-500 cursor-pointer transition-all duration-200`}
                onClick={() => handleSelectClient(client)}
              >
                <div className="flex justify-between">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">{client.name}</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingClient(client);
                      }}
                      className="text-gray-400 hover:text-primary-500 transition-colors duration-200"
                      title="Edit client"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClient(client.id);
                      }}
                      className="text-gray-400 hover:text-red-500 transition-colors duration-200"
                      title="Delete client"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  <p>{client.email}</p>
                  <p>{client.phone}</p>
                  <p className="mt-1 line-clamp-2">{client.address}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add New Client Form */}
        {isAddingClient && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="clientName" className={labelClasses}>Client Name</label>
                <input
                  type="text"
                  id="clientName"
                  value={newClient.name}
                  onChange={(e) => setNewClient({ ...newClient, name: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter client name"
                />
              </div>
              <div>
                <label htmlFor="clientEmail" className={labelClasses}>Email</label>
                <input
                  type="email"
                  id="clientEmail"
                  value={newClient.email}
                  onChange={(e) => setNewClient({ ...newClient, email: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter client email"
                />
              </div>
              <div>
                <label htmlFor="clientPhone" className={labelClasses}>Phone</label>
                <input
                  type="tel"
                  id="clientPhone"
                  value={newClient.phone}
                  onChange={(e) => setNewClient({ ...newClient, phone: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter client phone"
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="clientAddress" className={labelClasses}>Address</label>
                <textarea
                  id="clientAddress"
                  rows={3}
                  value={newClient.address}
                  onChange={(e) => setNewClient({ ...newClient, address: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter client address"
                />
              </div>
              <div>
                <label htmlFor="clientCompany" className={labelClasses}>Company</label>
                <input
                  type="text"
                  id="clientCompany"
                  value={newClient.company}
                  onChange={(e) => setNewClient({ ...newClient, company: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter company name"
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="clientNotes" className={labelClasses}>Notes</label>
                <textarea
                  id="clientNotes"
                  rows={2}
                  value={newClient.notes}
                  onChange={(e) => setNewClient({ ...newClient, notes: e.target.value })}
                  className={inputClasses}
                  placeholder="Enter any notes"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setIsAddingClient(false)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddClient}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save Client
              </button>
            </div>
          </div>
        )}

        {/* Edit Client Modal */}
        {editingClient && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Edit Client</h2>
                  <button
                    onClick={() => setEditingClient(null)}
                    className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  >
                    ✕
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className={labelClasses}>Client Name *</label>
                    <input
                      type="text"
                      value={editingClient.name}
                      onChange={(e) => setEditingClient({ ...editingClient, name: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter client name"
                      required
                    />
                  </div>

                  <div>
                    <label className={labelClasses}>Email</label>
                    <input
                      type="email"
                      value={editingClient.email || ''}
                      onChange={(e) => setEditingClient({ ...editingClient, email: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter email address"
                    />
                  </div>

                  <div>
                    <label className={labelClasses}>Phone</label>
                    <input
                      type="tel"
                      value={editingClient.phone || ''}
                      onChange={(e) => setEditingClient({ ...editingClient, phone: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter phone number"
                    />
                  </div>

                  <div>
                    <label className={labelClasses}>Company</label>
                    <input
                      type="text"
                      value={editingClient.company || ''}
                      onChange={(e) => setEditingClient({ ...editingClient, company: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter company name"
                    />
                  </div>

                  <div>
                    <label className={labelClasses}>Address</label>
                    <textarea
                      value={editingClient.address || ''}
                      onChange={(e) => setEditingClient({ ...editingClient, address: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter address"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className={labelClasses}>Notes</label>
                    <textarea
                      value={editingClient.notes || ''}
                      onChange={(e) => setEditingClient({ ...editingClient, notes: e.target.value })}
                      className={inputClasses}
                      placeholder="Enter any notes"
                      rows={2}
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setEditingClient(null)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleEditClient}
                      className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200"
                    >
                      Update Client
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientSection;