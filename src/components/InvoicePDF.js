import React from 'react';
import { Page, Document, Text, View, StyleSheet, Image, Font } from '@react-pdf/renderer';
import { getCurrencySymbol } from '../utils/currency';
import { loadCustomFonts } from '../utils/theme';

// Register standard fonts for PDF
Font.register({
  family: 'Helvetica',
  src: 'https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFVZ0e.ttf'
});

Font.register({
  family: 'Arial',
  src: 'https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFVZ0e.ttf'
});

Font.register({
  family: 'Courier New',
  src: 'https://fonts.gstatic.com/s/sourcecodepro/v14/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxlC9.ttf'
});

// Load custom fonts
const customFonts = loadCustomFonts();
customFonts.forEach(font => {
  try {
    Font.register({ family: font.name, src: font.url });
  } catch (error) {
    console.warn(`Failed to register font ${font.name}:`, error);
  }
});

const InvoicePDF = ({ invoice = {} }) => {
  // Destructure invoice data with default values
  const {
    companyInfo = {},
    selectedClient = {},
    items = [],
    invoiceNumber = '',
    issueDate = '',
    dueDate = '',
    terms = '',
    notes = '',
    currency = 'USD',
    theme = {}
  } = invoice;

  const currencySymbol = getCurrencySymbol(currency);
  const display = theme.display || {};

  // Calculate totals
  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + ((Number(item.quantity) || 0) * (Number(item.rate) || 0)), 0);
  };

  const subtotal = calculateSubtotal();
  const tax = display.showTax ? subtotal * 0.1 : 0; // 10% tax rate if enabled
  const total = subtotal + tax;

  // Create styles based on theme
  const styles = StyleSheet.create({
    page: {
      padding: 30,
      fontFamily: theme?.fontFamily || 'Helvetica',
      fontSize: parseInt(theme?.fontSize) || 12,
      color: theme?.secondaryColor || '#64748b',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    logo: {
      width: 150,
      height: 50,
      objectFit: 'contain',
    },
    companyInfo: {
      flex: 1,
      marginLeft: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 20,
      color: theme?.primaryColor || '#0ea5e9',
    },
    section: {
      marginBottom: 20,
    },
    grid: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: '#eee',
      paddingBottom: 5,
      paddingTop: 5,
    },
    col: {
      flex: 1,
    },
    colHeader: {
      backgroundColor: theme?.primaryColor || '#0ea5e9',
      color: 'white',
    },
    text: {
      fontSize: parseInt(theme?.fontSize) || 12,
    },
    bold: {
      fontWeight: 'bold',
    },
    rightAlign: {
      textAlign: 'right',
    },
    footer: {
      position: 'absolute',
      bottom: 30,
      left: 30,
      right: 30,
    },
    // Add any custom CSS provided by the user
    ...(theme.customCSS ? eval(`(${theme.customCSS})`) : {})
  });

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        {display.showHeader !== false && (
          <View style={styles.header}>
            {display.showLogo !== false && companyInfo?.logo && (
              <Image src={companyInfo.logo} style={styles.logo} />
            )}
            <View style={styles.companyInfo}>
              <Text style={styles.bold}>{companyInfo?.name || ''}</Text>
              <Text>{companyInfo?.address || ''}</Text>
              <Text>{companyInfo?.phone || ''}</Text>
              <Text>{companyInfo?.email || ''}</Text>
              <Text>{companyInfo?.website || ''}</Text>
            </View>
          </View>
        )}

        {/* Invoice Title */}
        <Text style={styles.title}>INVOICE</Text>

        {/* Invoice Details */}
        <View style={styles.section}>
          {display.showInvoiceNumber !== false && (
            <View style={styles.grid}>
              <View style={styles.col}>
                <Text style={styles.bold}>Invoice Number:</Text>
                <Text>{invoiceNumber || ''}</Text>
              </View>
            </View>
          )}
          {display.showDates !== false && (
            <View style={styles.grid}>
              <View style={styles.col}>
                <Text style={styles.bold}>Issue Date:</Text>
                <Text>{issueDate || ''}</Text>
              </View>
              <View style={styles.col}>
                <Text style={styles.bold}>Due Date:</Text>
                <Text>{dueDate || ''}</Text>
              </View>
            </View>
          )}
        </View>

        {/* Client Info */}
        <View style={styles.section}>
          <Text style={styles.bold}>Bill To:</Text>
          <Text>{selectedClient?.name || null}</Text>
          <Text>{selectedClient?.address || null}</Text>
          <Text>{selectedClient?.email || null}</Text>
          <Text>{selectedClient?.phone || null}</Text>
        </View>

        {/* Items */}
        <View style={styles.section}>
          <View style={[styles.grid, styles.colHeader]}>
            {display.showItemDescription !== false && (
              <Text style={[styles.col, styles.bold]}>Description</Text>
            )}
            <Text style={[{ flex: 0.5 }, styles.bold, styles.rightAlign]}>Qty</Text>
            <Text style={[{ flex: 0.5 }, styles.bold, styles.rightAlign]}>Rate</Text>
            <Text style={[{ flex: 0.5 }, styles.bold, styles.rightAlign]}>Amount</Text>
          </View>
          {items.map((item, index) => (
            <View key={index} style={styles.grid}>
              {display.showItemDescription !== false && (
                <Text style={styles.col}>{item.description || null}</Text>
              )}
              <Text style={[{ flex: 0.5 }, styles.rightAlign]}>{item.quantity || null}</Text>
              <Text style={[{ flex: 0.5 }, styles.rightAlign]}>
                {currencySymbol} {(Number(item.rate) || 0).toFixed(2)}
              </Text>
              <Text style={[{ flex: 0.5 }, styles.rightAlign]}>
                {currencySymbol} {((Number(item.quantity) || 0) * (Number(item.rate) || 0)).toFixed(2)}
              </Text>
            </View>
          ))}
        </View>

        {/* Totals */}
        <View style={[styles.section, { alignItems: 'flex-end' }]}>
          <View style={{ width: '50%' }}>
            <View style={styles.grid}>
              <Text style={styles.col}>Subtotal:</Text>
              <Text style={styles.rightAlign}>
                {currencySymbol} {subtotal.toFixed(2)}
              </Text>
            </View>
            {display.showTax !== false && (
              <View style={styles.grid}>
                <Text style={styles.col}>Tax (10%):</Text>
                <Text style={styles.rightAlign}>
                  {currencySymbol} {tax.toFixed(2)}
                </Text>
              </View>
            )}
            <View style={[styles.grid, styles.bold]}>
              <Text style={styles.col}>Total:</Text>
              <Text style={styles.rightAlign}>
                {currencySymbol} {total.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* Terms and Notes */}
        {display.showNotes !== false && (terms || notes) && (
          <View style={styles.section}>
            {terms && (
              <>
                <Text style={styles.bold}>Terms:</Text>
                <Text>{terms}</Text>
              </>
            )}
            {notes && (
              <>
                <Text style={[styles.bold, { marginTop: 10 }]}>Notes:</Text>
                <Text>{notes}</Text>
              </>
            )}
          </View>
        )}

        {/* Payment Info */}
        {display.showPaymentDetails !== false && (
          <View style={styles.footer}>
            <Text style={styles.bold}>Payment Details:</Text>
            <Text>Bank: {companyInfo?.paymentInfo?.bankName || ''}</Text>
            <Text>Account Name: {companyInfo?.paymentInfo?.accountName || ''}</Text>
            <Text>Account Number: {companyInfo?.paymentInfo?.accountNumber || ''}</Text>
            <Text>Sort Code: {companyInfo?.paymentInfo?.sortCode || ''}</Text>
            <Text>IBAN: {companyInfo?.paymentInfo?.iban || ''}</Text>
            <Text>SWIFT/BIC: {companyInfo?.paymentInfo?.swift || ''}</Text>
          </View>
        )}

        {/* Signature Line */}
        {display.showSignature && (
          <View style={[styles.section, { marginTop: 50 }]}>
            <View style={[styles.grid, { borderTopWidth: 1, borderTopColor: '#eee', paddingTop: 10 }]}>
              <Text>Authorized Signature</Text>
              <Text style={styles.rightAlign}>Date</Text>
            </View>
          </View>
        )}
      </Page>
    </Document>
  );
};

export default InvoicePDF;