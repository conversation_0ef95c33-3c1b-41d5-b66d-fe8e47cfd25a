import { Link } from 'react-router-dom';

const quickAccessItems = [
  { title: 'Create Invoice', path: '/invoices', description: 'Create and manage your invoices' },
  { title: 'Clients', path: '/clients', description: 'Manage your client database' },
  { title: 'Settings', path: '/settings', description: 'Configure application preferences' },
];

export default function LandingPage() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%)',
      fontFamily: 'Segoe UI, sans-serif'
    }}>
      <header style={{
        padding: '40px 0 20px 0',
        textAlign: 'center',
        background: 'white',
        boxShadow: '0 2px 8px rgba(0,0,0,0.03)'
      }}>
        <h1 style={{
          fontSize: '2.8rem',
          margin: 0,
          fontWeight: 700,
          letterSpacing: '-1px',
          color: '#2d3748'
        }}>
          Invoice Generator
        </h1>
        <p style={{
          fontSize: '1.2rem',
          color: '#4a5568',
          margin: '18px 0 0 0'
        }}>
          Simple • Secure • Professional
        </p>
        <a
          href="https://github.com/your-repo"
          target="_blank"
          rel="noopener noreferrer"
          style={{
            display: 'inline-block',
            marginTop: '18px',
            padding: '10px 22px',
            background: '#24292f',
            color: 'white',
            borderRadius: '6px',
            textDecoration: 'none',
            fontWeight: 500,
            transition: 'background 0.2s'
          }}
        >
          ⭐ Star on GitHub
        </a>
      </header>

      <main style={{ maxWidth: '900px', margin: '0 auto', padding: '40px 20px 0 20px' }}>
        <section style={{
          background: 'white',
          borderRadius: '12px',
          boxShadow: '0 2px 12px rgba(0,0,0,0.04)',
          padding: '32px',
          marginBottom: '40px',
          textAlign: 'center'
        }}>
          <h2 style={{ fontSize: '1.5rem', color: '#2b6cb0', margin: 0 }}>
            Create Professional Invoices
          </h2>
          <p style={{ color: '#4a5568', fontSize: '1.1rem', margin: '18px 0 0 0' }}>
            Generate, customize, and send invoices in minutes. <br />
            <b>No sign-up required.</b>
          </p>
        </section>

        <nav>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
            gap: '28px'
          }}>
            {quickAccessItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                style={{
                  padding: '28px 20px',
                  background: '#f7fafc',
                  border: '1px solid #e2e8f0',
                  borderRadius: '10px',
                  textDecoration: 'none',
                  color: '#2d3748',
                  boxShadow: '0 1px 4px rgba(0,0,0,0.03)',
                  transition: 'all 0.2s ease-in-out',
                  display: 'block'
                }}
                onMouseOver={e => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 4px 16px rgba(45,55,72,0.08)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 1px 4px rgba(0,0,0,0.03)';
                }}
              >
                <h3 style={{ margin: '0 0 10px 0', fontSize: '1.2rem', fontWeight: 600 }}>{item.title}</h3>
                <p style={{ margin: 0, color: '#718096' }}>{item.description}</p>
              </Link>
            ))}
          </div>
        </nav>
      </main>

      <footer style={{
        marginTop: '60px',
        padding: '24px 0',
        textAlign: 'center',
        color: '#a0aec0',
        fontSize: '1rem'
      }}>
        Version 1.0 • MIT Licensed • <a href="https://github.com/your-repo" style={{ color: '#3182ce' }}>Contribute on GitHub</a>
      </footer>
    </div>
  );
}