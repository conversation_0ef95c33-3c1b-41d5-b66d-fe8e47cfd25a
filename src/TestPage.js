import React from 'react';

const TestPage = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        background: 'white',
        padding: '3rem',
        borderRadius: '20px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <h1 style={{
          fontSize: '2.5rem',
          color: '#333',
          marginBottom: '1rem',
          fontWeight: 'bold'
        }}>
          🎉 It's Working!
        </h1>
        <p style={{
          fontSize: '1.2rem',
          color: '#666',
          marginBottom: '2rem',
          lineHeight: '1.6'
        }}>
          Your React application is running successfully on port 3002!
        </p>
        <div style={{
          background: '#f8f9fa',
          padding: '1rem',
          borderRadius: '10px',
          marginBottom: '2rem'
        }}>
          <p style={{ margin: 0, color: '#333' }}>
            <strong>Server Status:</strong> ✅ Running<br/>
            <strong>Port:</strong> 3002<br/>
            <strong>Time:</strong> {new Date().toLocaleTimeString()}
          </p>
        </div>
        <button 
          onClick={() => window.location.reload()}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '25px',
            fontSize: '1rem',
            cursor: 'pointer',
            fontWeight: 'bold',
            transition: 'transform 0.2s'
          }}
          onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}
          onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
        >
          🔄 Refresh Page
        </button>
      </div>
    </div>
  );
};

export default TestPage;
