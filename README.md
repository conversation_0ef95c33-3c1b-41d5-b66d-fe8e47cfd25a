# 💼 Divided Finance Master

> **Complete Open Source Invoicing System**
> Beautiful, intuitive, and completely private invoice generator with all data stored locally in your browser.

![Divided Finance Master](./public/images/logo.png)

## 🌟 Features

- 🚀 **Lightning Fast** - Create professional invoices in under 2 minutes
- 🎨 **Beautiful Design** - Stunning, customizable templates with sophisticated color palette
- 💾 **Auto-Save** - Never lose your work with automatic saving
- 📱 **Mobile Ready** - Works perfectly on all devices
- 🔒 **Privacy First** - All data stored locally in your browser - zero server storage
- 📊 **Smart Analytics** - Track your invoicing performance with built-in dashboard
- 🔓 **Open Source** - Complete transparency and customization
- 🎯 **No Sign-up Required** - Start creating invoices immediately
- 🌙 **Dark Mode Support** - Beautiful dark theme included
- 💰 **Multiple Currencies** - Support for global currencies
- 📑 **PDF Export** - Professional PDF generation
- 👥 **Client Management** - Organize and manage your clients

## Live Demo

Access the live demo [here](https://your-username.github.io/invoice-generator)

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
git clone https://github.com/your-username/invoice-generator.git
cd invoice-generator

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm start
# or
yarn start
```

4. Build for production:
```bash
npm run build
# or
yarn build
```

## Usage

1. **Company Information**
   - Add your company details
   - Upload company logo
   - Set payment information

2. **Client Management**
   - Add new clients
   - Select existing clients
   - Manage client database

3. **Invoice Items**
   - Add/remove items
   - Set quantities and prices
   - Add tax rates
   - Multiple currency support

4. **Theme Customization**
   - Change primary colors
   - Customize fonts
   - Adjust layout elements

5. **Export Options**
   - Download as PDF
   - Print directly
   - Save to browser storage

## Tech Stack

- React
- Tailwind CSS
- @react-pdf/renderer
- react-color
- react-toastify
- currency-symbol-map

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- React Team
- Tailwind CSS Team
- All contributors and supporters

## Support

For support, email <EMAIL> or open an issue in the repository.
